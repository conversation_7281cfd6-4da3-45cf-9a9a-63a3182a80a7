import { TextField } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import Tooltip from '@mui/material/Tooltip';
import { createLineFormUtils } from '@src/components/LineFormInputs/createLineFormUtils';
import type { GcrFltLine, GcrFltMessage } from '@src/forms/gcrFltForm/gcrFltTypes';
import type { GcrRegLine, GcrRegMessage } from '@src/forms/gcrRegForm/gcrRegTypes';
import type { ScrLine, ScrMessage } from '@src/forms/scrForm/scrTypes';
import type { BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig } from '@src/typesGlobal';
import { useMemo } from 'react';

/**
 * Work in Progress:
 *
 * Goal: Replace the factory pattern (createLineTextField) with a generic LineTextField component
 * that supports all line types (<PERSON>r<PERSON>ine, GcrFltLine, GcrRegLine) using explicit union types.
 *
 * Benefits over factory approach:
 * - More explicit and easier to understand for developers (including myself 😂)
 * - No abstraction layer hiding the store usage
 * - Standard React component pattern vs factory pattern
 * - Better IDE support and TypeScript inference
 * - Simpler testing - just test a component with props vs testing a factory
 *
 * Usage will be:
 * <LineTextField
 *   fieldKey="someField"
 *   useStore={useScrStore}  // Pass the specific hook for the form type
 *   formConfig={scrFormConfig}
 * />
 */

export type AllLines = ScrLine | GcrFltLine | GcrRegLine;
export type AllMessages = ScrMessage | GcrFltMessage | GcrRegMessage;

export type LineTextFieldProps<TLine, TMessage> = React.ComponentProps<typeof TextField> & {
  currentInputKey: keyof TLine;
  useStore: () => BaseFormStore<TLine, TMessage>;
  formConfig: FormConfig<TLine>;
};

export const LineTextField = <TLine, TMessage>(props: LineTextFieldProps<TLine, TMessage>) => {
  const { currentInputKey, useStore, formConfig, ...rest } = props;

  const { formLine, setFormLinePartial, setFormLineFocusKey, formLineFocusKey, formLineErrors: errors } = useStore(); // Call the hook

  const { onChange, onKeyDown, inputs } = useMemo(() => {
    const { createOnChange, createOnKeyHandler, inputs } = createLineFormUtils({ formConfig });
    return {
      inputs,
      onChange: createOnChange(currentInputKey, setFormLinePartial),
      onKeyDown: createOnKeyHandler(formConfig.inputs, currentInputKey, setFormLinePartial, setFormLineFocusKey),
    };
  }, [formConfig, currentInputKey, setFormLinePartial, setFormLineFocusKey]);

  const thisInput = inputs[currentInputKey];
  // Check if this is a select field:
  const isSelect = thisInput.type === 'select';
  // Check for errors:
  const error = errors?.[currentInputKey as string] || '';
  const hasError = !!error;

  return (
    <Tooltip title={error}>
      <TextField
        label={thisInput.label}
        name={thisInput.storeKey as string}
        select={isSelect}
        value={formLine?.[currentInputKey] || ''}
        onChange={onChange}
        onKeyDown={onKeyDown}
        focused={formLineFocusKey === currentInputKey}
        error={hasError}
        // ... other props
        {...rest}
      >
        {isSelect &&
          thisInput.options?.map((option) => (
            <MenuItem key={option} value={option}>
              {option === '' ? '\u00A0' : option} {/* Non-breaking space for full height of option */}
            </MenuItem>
          ))}
      </TextField>
    </Tooltip>
  );
};
