import { Add, Close } from '@mui/icons-material';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { LineErrors } from '@src/components/LineErrors';
import { DEBUG } from '@src/constants';
import { formatDoop } from '@src/utils/utils';
import { ScrLineTextField } from './SrcLineTextField';
import { mockLines } from './scrMockData';
import { useScrStore } from './useScrStore';

type ScrLineFormProps = {
  handleAddLine: () => void;
};

export const ScrLineFormTableRenderer: React.FC<ScrLineFormProps> = (props) => {
  const { handleAddLine } = props;

  const { formLineIndex, clearFormLine, setFormLinePartial } = useScrStore();

  const isEdit = formLineIndex !== null;

  return (
    <>
      <Table size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '2%' }} /* Line header */ />
          <col style={{ width: '3%' }} /* Action */ />
          <col style={{ width: '3%' }} /* Operator */ />
          <col style={{ width: '4%' }} /* Flight Number */ />
          <col style={{ width: '8%' }} /* Date Range */ />
          {/* 5 */}
          <col style={{ width: '4%' }} /* DOOP */ />
          <col style={{ width: '4%' }} /* Seats */ />
          <col style={{ width: '4%' }} /* Aircraft Type */ />
          <col style={{ width: '4%' }} /* Origin */ />
          <col style={{ width: '4%' }} /* Previous */ />
          {/* 10 */}
          <col style={{ width: '4%' }} /* Time */ />
          <col style={{ width: '3%' }} /* O/N */ />
          <col style={{ width: '4%' }} /* Next */ />
          <col style={{ width: '4%' }} /* Destination */ />
          <col style={{ width: '3%' }} /* Service Type*/ />
          {/* 15 */}
          <col style={{ width: '3%' }} /* Frequency */ />
          <col style={{ width: '5%' }} /* Aircraft Registration */ />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell align="center">C/R/N/D</TableCell>
            <TableCell>Operator</TableCell>
            <TableCell>FltNo</TableCell>
            <TableCell sx={{ minWidth: '10rem' }}>Date Range</TableCell>
            {/* 5 */}
            <TableCell>DOOP</TableCell>
            <TableCell align="center">Seats</TableCell>
            <TableCell align="center">A/C</TableCell>
            <TableCell align="center">Origin</TableCell>
            <TableCell align="center">Prev</TableCell>
            {/* 10 */}
            <TableCell align="center">Time</TableCell>
            <TableCell align="center">O/N</TableCell>
            <TableCell align="center">Next</TableCell>
            <TableCell align="center">Dest</TableCell>
            <TableCell align="center">St</TableCell>
            {/* 15 */}
            <TableCell align="center">Frq</TableCell>
            <TableCell align="center">Acreg</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Arr
            </TableCell>
            {/* Action */}
            <TableCell rowSpan={2}>
              <ScrLineTextField currentInputKey="action" />
            </TableCell>
            {/* Operator */}
            <TableCell>
              <ScrLineTextField currentInputKey="operatorArr" />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <ScrLineTextField currentInputKey="flightNumberArr" />
            </TableCell>
            {/* Date Range */}
            <TableCell rowSpan={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <ScrLineTextField currentInputKey="dateFrom" sx={{ maxWidth: '5rem' }} />
                <ScrLineTextField currentInputKey="dateTo" sx={{ maxWidth: '5rem' }} />
              </Box>
            </TableCell>
            {/* DOOP */}
            <TableCell rowSpan={2}>
              <ScrLineTextField
                currentInputKey="doop"
                onBlur={(e) => {
                  // Convert DOOP shorthand to full format on blur
                  setFormLinePartial({ doop: formatDoop(e.target.value) });
                }}
              />
            </TableCell>
            {/* Seats */}
            <TableCell rowSpan={2}>
              <ScrLineTextField currentInputKey="seats" />
            </TableCell>
            {/* Aircraft Type */}
            <TableCell rowSpan={2}>
              <ScrLineTextField currentInputKey="aircraftType" />
            </TableCell>
            {/* Origin */}
            <TableCell>
              <ScrLineTextField currentInputKey="origin" />
            </TableCell>
            {/* Previous */}
            <TableCell>
              <ScrLineTextField currentInputKey="previous" />
            </TableCell>
            {/* Time 1 */}
            <TableCell>
              <ScrLineTextField currentInputKey="timeArr" />
            </TableCell>
            {/* O/N */}
            <TableCell>{/* O/N */}</TableCell>
            {/* Next */}
            <TableCell>{/* Next */}</TableCell>
            {/* Destination */}
            <TableCell>{/* Dest */}</TableCell>
            {/* Service Type*/}
            <TableCell>
              <ScrLineTextField currentInputKey="stArr" />
            </TableCell>
            {/* Frequency */}
            <TableCell rowSpan={2}>
              <ScrLineTextField currentInputKey="frequency" />
            </TableCell>
            {/* Aircraft Registration */}
            <TableCell rowSpan={2}>
              <ScrLineTextField currentInputKey="aircraftRegistration" />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Dep
            </TableCell>
            {/* Action */}
            {/* rowSpan={2} */}
            {/* Operator */}
            <TableCell>
              <ScrLineTextField currentInputKey="operatorDep" />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <ScrLineTextField currentInputKey="flightNumberDep" />
            </TableCell>
            {/* Date Range */}
            {/* rowSpan={2} */}
            {/* DOOP */}
            {/* rowSpan={2} */}
            {/* Seats */}
            {/* rowSpan={2} */}
            {/* Aircraft Type */}
            {/* rowSpan={2} */}
            {/* Origin */}
            <TableCell>{/* Origin */}</TableCell>
            {/* Previous */}
            <TableCell>{/* Prev */}</TableCell>
            {/* Time 2 */}
            <TableCell>
              <ScrLineTextField currentInputKey="timeDep" />
            </TableCell>
            {/* O/N */}
            <TableCell>
              {/* O/N */}
              <ScrLineTextField currentInputKey="on" />
            </TableCell>
            {/* Next */}
            <TableCell>
              {/* Next */}
              <ScrLineTextField currentInputKey="next" />
            </TableCell>
            {/* Destination */}
            <TableCell>
              {/* Dest */}
              <ScrLineTextField currentInputKey="destination" />
            </TableCell>
            {/* Service Type*/}
            <TableCell>
              <ScrLineTextField currentInputKey="stDep" />
            </TableCell>
            {/* Frequency */}
            {/* rowSpan={2} */}
            {/* Aircraft Registration */}
            {/* rowSpan={2} */}
          </TableRow>
        </TableBody>
      </Table>

      <LineErrors useStore={useScrStore} />

      {DEBUG && (
        <TableFormInnerBox className="right">
          {mockLines.map((line, i) => (
            <MockButton key={i} onClick={() => setFormLinePartial({ ...line })}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}

      <TableFormInnerBox className="right">
        <FormButton color="secondary" onClick={() => clearFormLine()} startIcon={<Close />}>
          Clear
        </FormButton>
        <FormButton onClick={handleAddLine} startIcon={<Add />}>
          {isEdit ? `Update Line` : `Add Line`}
        </FormButton>
      </TableFormInnerBox>
    </>
  );
};
