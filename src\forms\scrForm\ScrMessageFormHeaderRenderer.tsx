import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import { useRef } from 'react';
import { scrMessageFormConfig as formConfig } from './scrMessageFormConfig';
import { getOnChange } from './scrMessageFormUtils';
import { useScrStore } from './useScrStore';
import Tooltip from '@mui/material/Tooltip';

export const ScrMessageFormHeaderRenderer: React.FC = () => {
  const { inputs } = formConfig;
  const { formMessage, setFormMessagePartial: setMsgPart, formMessageErrors: errors } = useScrStore();

  const formRef = useRef<HTMLDivElement>(null);

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '20%' }} />
          <col style={{ width: '20%' }} />
          <col style={{ width: '15%' }} />
          <col style={{ width: '45%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.season.label}</TableCell>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.date.label}</TableCell>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.airport.label}</TableCell>
            <TableCell sx={{ textAlign: 'left' }}>{formConfig.inputs.creator.label}</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <Tooltip title={errors?.season}>
                <TextField
                  value={formMessage?.season || ''}
                  name="message.season"
                  onChange={getOnChange(inputs.season.storeKey, setMsgPart)}
                  error={!!errors?.season}
                />
              </Tooltip>
            </TableCell>
            <TableCell>
              <Tooltip title={errors?.date}>
                <TextField
                  value={formMessage?.date || ''}
                  name="message.date"
                  onChange={getOnChange(inputs.date.storeKey, setMsgPart)}
                  error={!!errors?.date}
                />
              </Tooltip>
            </TableCell>
            <TableCell>
              <Tooltip title={errors?.airport}>
                <TextField
                  value={formMessage?.airport || ''}
                  name="message.airport"
                  onChange={getOnChange(inputs.airport.storeKey, setMsgPart)}
                  error={!!errors?.airport}
                />
              </Tooltip>
            </TableCell>
            <TableCell>
              <Tooltip title={errors?.creator}>
                <TextField
                  name="message.creator"
                  onBlur={(e) => {
                    setMsgPart({ creator: e.target.value });
                  }}
                  error={!!errors?.creator}
                />
              </Tooltip>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
