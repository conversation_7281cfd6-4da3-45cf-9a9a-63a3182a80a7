import styled from '@emotion/styled';
import AppBar from '@mui/material/AppBar';
import Button from '@mui/material/Button';
import Container from '@mui/material/Container';
import Paper from '@mui/material/Paper';
import { useTheme } from '@mui/material/styles';
import Toolbar from '@mui/material/Toolbar';
import Typography from '@mui/material/Typography';
import { appPages, PAGE_QUERY_PARAM } from '@src/constants';
import { useCallback, useMemo } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

export const PageSectionWrap = styled(Paper)`
  overflow-x: auto;
  padding: 0.2rem;
  margin: 0 0 1.5rem 0;
  background-color: #f0f9ff;
  border: 1px solid #ccc;
`;

export const Layout: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const activeColor = 'rgba(255, 255, 255, 0.1)';

  // Get current page from query parameters (case insensitive)
  const searchParams = new URLSearchParams(location.search);
  const currentPageParam = searchParams.get(PAGE_QUERY_PARAM)?.toLowerCase() || 'home';

  // Find the current page component
  const currentPage = useMemo(() => {
    return appPages.find((page) => page.pageParam.toLowerCase() === currentPageParam) || appPages[0];
  }, [currentPageParam]);

  /** Replace current page in the query parameters and navigate to the new page. */
  const handleNavigation = useCallback(
    (pageParam: string) => {
      const newSearchParams = new URLSearchParams(location.search);
      if (pageParam === 'home') {
        newSearchParams.delete(PAGE_QUERY_PARAM);
      } else {
        newSearchParams.set(PAGE_QUERY_PARAM, pageParam);
      }
      const newSearch = newSearchParams.toString();
      // Build the navigation path:
      const path = newSearch ? `/?${newSearch}` : '/';
      navigate(path);
    },
    [location.search, navigate],
  );

  return (
    <div style={{ minWidth: 'fit-content', width: '100%' }}>
      {/* AppBar: sx={{ width: '100%', minWidth: 'fit-content' }} */}
      <AppBar position="static">
        {/* Toolbar: sx={{ width: '100%', minWidth: 'fit-content' }} */}
        <Toolbar sx={{ 
          [theme.breakpoints.up('xs')]: {
            width: '30rem',
          },
          [theme.breakpoints.up('sm')]: {
            width: '40rem',
          },
          [theme.breakpoints.up('md')]: {
            width: '50rem',
          },
          [theme.breakpoints.up('lg')]: {
            width: '60rem',
          },
          [theme.breakpoints.up('xl')]: {
            width: '70rem',
          },
        }}>
          <Typography variant="h4" sx={{ flexGrow: 1 }}>
            Aviation Messages
          </Typography>
          {appPages.map((page) => (
            <Button
              key={page.name}
              color="inherit"
              onClick={() => handleNavigation(page.pageParam)}
              sx={{
                bgcolor: currentPage.pageParam === page.pageParam ? activeColor : 'transparent',
                fontSize: '0.9rem',
              }}
            >
              {page.name}
            </Button>
          ))}
        </Toolbar>
      </AppBar>
      <Container
        maxWidth={false}
        sx={{
          mt: 4,
          width: '100%',
          minWidth: 'fit-content',
          // bgcolor: 'lime',
          [theme.breakpoints.up('xs')]: {
            maxWidth: '100%',
          },
          [theme.breakpoints.up('sm')]: {
            maxWidth: '100%',
          },
          [theme.breakpoints.up('md')]: {
            maxWidth: '100%',
          },
          [theme.breakpoints.up('lg')]: {
            maxWidth: '100%',
          },
        }}
      >
        <currentPage.component />
      </Container>
    </div>
  );
};
