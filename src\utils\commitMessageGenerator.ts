/**
 * Commit Message Generator
 * 
 * Generates consistent commit messages in imperative format with:
 * - Short header line
 * - Bullet points starting with dash (-)
 * - Analysis of staged files to determine appropriate message
 */

export type FileChange = {
  status: 'A' | 'M' | 'D' | 'R' | 'C' | '??' | string;
  path: string;
};

export type CommitMessageOptions = {
  /** Custom header override */
  customHeader?: string;
  /** Additional context for the commit */
  context?: string;
  /** Maximum number of files to detail in bullets */
  maxFileDetails?: number;
};

/**
 * Analyzes file changes and generates appropriate commit message
 */
export const generateCommitMessage = (
  changes: FileChange[],
  options: CommitMessageOptions = {}
): string => {
  const { customHeader, context, maxFileDetails = 5 } = options;

  if (changes.length === 0) {
    return 'Update project files';
  }

  // Categorize changes
  const added = changes.filter(c => c.status === 'A');
  const modified = changes.filter(c => c.status === 'M');
  const deleted = changes.filter(c => c.status === 'D');
  const renamed = changes.filter(c => c.status === 'R');

  // Generate header
  const header = customHeader || generateHeader(changes, { added, modified, deleted, renamed });

  // Generate bullet points
  const bullets = generateBullets(changes, { added, modified, deleted, renamed }, maxFileDetails);

  // Combine header and bullets
  const message = [header, ...bullets].join('\n');

  return context ? `${message}\n\n${context}` : message;
};

/**
 * Generates the header line based on file changes
 */
const generateHeader = (
  changes: FileChange[],
  categorized: {
    added: FileChange[];
    modified: FileChange[];
    deleted: FileChange[];
    renamed: FileChange[];
  }
): string => {
  const { added, modified, deleted, renamed } = categorized;
  const totalChanges = changes.length;

  // Determine primary action
  if (totalChanges === 1) {
    const change = changes[0];
    const fileName = getFileName(change.path);
    
    switch (change.status) {
      case 'A':
        return `Add ${fileName}`;
      case 'M':
        return `Update ${fileName}`;
      case 'D':
        return `Remove ${fileName}`;
      case 'R':
        return `Rename ${fileName}`;
      default:
        return `Modify ${fileName}`;
    }
  }

  // Multiple files - determine dominant action
  if (modified.length > added.length && modified.length > deleted.length) {
    return `Update ${getFileTypeDescription(modified)} configuration`;
  }
  
  if (added.length > 0 && deleted.length === 0) {
    return `Add ${getFileTypeDescription(added)}`;
  }
  
  if (deleted.length > 0 && added.length === 0) {
    return `Remove ${getFileTypeDescription(deleted)}`;
  }
  
  if (added.length > 0 && deleted.length > 0) {
    return `Refactor ${getFileTypeDescription(changes)}`;
  }

  return `Update ${getFileTypeDescription(changes)}`;
};

/**
 * Generates bullet points for the commit message
 */
const generateBullets = (
  changes: FileChange[],
  categorized: {
    added: FileChange[];
    modified: FileChange[];
    deleted: FileChange[];
    renamed: FileChange[];
  },
  maxDetails: number
): string[] => {
  const { added, modified, deleted, renamed } = categorized;
  const bullets: string[] = [];

  // Add bullets for each category
  if (added.length > 0) {
    bullets.push(...generateCategoryBullets('Add', added, maxDetails));
  }
  
  if (modified.length > 0) {
    bullets.push(...generateCategoryBullets('Update', modified, maxDetails));
  }
  
  if (deleted.length > 0) {
    bullets.push(...generateCategoryBullets('Remove', deleted, maxDetails));
  }
  
  if (renamed.length > 0) {
    bullets.push(...generateCategoryBullets('Rename', renamed, maxDetails));
  }

  return bullets;
};

/**
 * Generates bullets for a specific category of changes
 */
const generateCategoryBullets = (
  action: string,
  changes: FileChange[],
  maxDetails: number
): string[] => {
  if (changes.length === 0) return [];

  const bullets: string[] = [];
  const displayChanges = changes.slice(0, maxDetails);
  
  displayChanges.forEach(change => {
    const fileName = getFileName(change.path);
    const fileType = getFileType(change.path);
    
    if (fileType) {
      bullets.push(`- ${action} ${fileName} ${fileType}`);
    } else {
      bullets.push(`- ${action} ${fileName}`);
    }
  });

  // Add summary if there are more files
  if (changes.length > maxDetails) {
    const remaining = changes.length - maxDetails;
    bullets.push(`- ${action} ${remaining} additional file${remaining > 1 ? 's' : ''}`);
  }

  return bullets;
};

/**
 * Extracts filename from path
 */
const getFileName = (path: string): string => {
  return path.split('/').pop() || path.split('\\').pop() || path;
};

/**
 * Determines file type description
 */
const getFileType = (path: string): string => {
  const ext = path.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'ts':
    case 'tsx':
      return 'TypeScript file';
    case 'js':
    case 'jsx':
      return 'JavaScript file';
    case 'json':
      return 'configuration';
    case 'md':
      return 'documentation';
    case 'css':
    case 'scss':
      return 'styles';
    case 'html':
      return 'template';
    case 'yml':
    case 'yaml':
      return 'configuration';
    default:
      return '';
  }
};

/**
 * Gets description for a group of files
 */
const getFileTypeDescription = (changes: FileChange[]): string => {
  const paths = changes.map(c => c.path);
  
  // Check for common patterns
  if (paths.some(p => p.includes('Form') && p.includes('Config'))) {
    return 'form configuration';
  }
  
  if (paths.some(p => p.includes('component') || p.includes('Component'))) {
    return 'components';
  }
  
  if (paths.some(p => p.includes('util') || p.includes('Utils'))) {
    return 'utilities';
  }
  
  if (paths.some(p => p.includes('store') || p.includes('Store'))) {
    return 'store configuration';
  }
  
  if (paths.some(p => p.includes('validation') || p.includes('Validation'))) {
    return 'validation';
  }

  // Default to file count
  return `${changes.length} file${changes.length > 1 ? 's' : ''}`;
};

/**
 * Parses git status porcelain output into FileChange objects
 */
export const parseGitStatus = (statusOutput: string): FileChange[] => {
  return statusOutput
    .split('\n')
    .filter(line => line.trim())
    .map(line => {
      const status = line.substring(0, 2).trim();
      const path = line.substring(3);
      return { status, path };
    });
};

/**
 * Common commit message templates
 */
export const commitTemplates = {
  feature: (feature: string) => `Add ${feature} feature`,
  bugfix: (issue: string) => `Fix ${issue}`,
  refactor: (component: string) => `Refactor ${component}`,
  update: (component: string) => `Update ${component}`,
  remove: (component: string) => `Remove ${component}`,
  docs: (section: string) => `Update ${section} documentation`,
  config: (type: string) => `Update ${type} configuration`,
  deps: (action: string) => `${action} dependencies`,
};
