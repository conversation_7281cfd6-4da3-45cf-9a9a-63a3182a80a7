import { isNil } from 'lodash';
import type { GcrFltLine, GcrFltMessage } from './gcrFltTypes';

const val2str = <T = unknown>(val: T): string => (isNil(val) ? '' : String(val));

export const formatGcrFltLine = (line: GcrFltLine): string => {
  const action = val2str(line.action);
  const operatorArr = val2str(line.operatorArr);
  const flightNumberArr = val2str(line.flightNumberArr?.padStart(4, '0'));
  const operatorDep = val2str(line.operatorDep);
  const flightNumberDep = val2str(line.flightNumberDep?.padStart(4, '0'));
  const date = val2str(line.date);
  const seats = val2str(line.seats?.toString().padStart(3, '0'));
  const aircraftType = val2str(line.aircraftType);
  const origin = val2str(line.origin);
  const previous = val2str(line.previous);
  const timeArr = val2str(line.timeArr);
  const timeDep = val2str(line.timeDep);
  const on = line?.on === 0 ? '' : val2str(line?.on);
  const next = val2str(line.next);
  const destination = val2str(line.destination);
  const stArr = val2str(line.stArr);
  const stDep = val2str(line.stDep);
  const aircraftRegistration = val2str(line.aircraftRegistration);
  const acReg = aircraftRegistration ? `/ ${aircraftRegistration}/` : '';

  // Whether there is an arrival and departure:
  const isArr = origin || previous || timeArr || stArr;
  const isDep = timeDep || on || next || destination || stDep;
  // Create the arrival and departure strings:
  const arr1 = isArr ? ` ${origin}${previous}${timeArr} ` : '';
  const dep1 = isDep ? ` ${timeDep}${on}${next}${destination} ` : '';
  // If there is a departure but no arrival, we need a gap:
  const gap = isDep && !isArr ? ' ' : '';

  let result =
    `${action}${gap}${operatorArr}${flightNumberArr}` +
    ` ${operatorDep}${flightNumberDep}` +
    ` ${date} ${seats}${aircraftType}` +
    `${arr1}${dep1}` +
    ` ${stArr}${stDep} ${acReg}`;

  // Remove double spaces
  result = result.replace(/ {2,}/g, ' ').trim();

  // console.log({ result });
  return result;
};

export const formatGcrFltMsgHeader = (msg: GcrFltMessage): string => {
  return `GCR\n/FLT\n${val2str(msg.airport)}`;
};

export const formatGcrFltMsgFooter = (msg: GcrFltMessage): string => {
  return `SI ${val2str(msg.si)}\nGI ${val2str(msg.gi)}`;
};

export const formatGcrFltMessage = (msg: GcrFltMessage, lines: GcrFltLine[]): string => {
  const header = formatGcrFltMsgHeader(msg);
  const linesAsStrings = lines.map(formatGcrFltLine);
  const footer = formatGcrFltMsgFooter(msg);
  return [header, ...linesAsStrings, footer].join('\n');
};
