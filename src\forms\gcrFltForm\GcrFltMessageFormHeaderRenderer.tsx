import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import { useRef } from 'react';
import { gcrFltMessageFormConfig as formConfig } from './gcrFltMessageFormConfig';
import { getOnChange } from './gcrFltMessageFormUtils';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltMessageFormHeaderRenderer: React.FC = () => {
  const { inputs } = formConfig;
  const { formMessage, setFormMessagePartial: setMsgPart, formMessageErrors: errors } = useGcrFltStore();

  const formRef = useRef<HTMLDivElement>(null);

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '25%' }} />
          <col style={{ width: '25%' }} />
          <col style={{ width: '25%' }} />
          <col style={{ width: '25%' }} />
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell sx={{ textAlign: 'left' }} colSpan={4}>
              {formConfig.inputs.airport.label}
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell>
              <Tooltip title={errors?.airport}>
                <TextField
                  value={formMessage?.airport || ''}
                  name="message.airport"
                  onChange={getOnChange(inputs.airport.storeKey, setMsgPart)}
                  error={!!errors?.airport}
                />
              </Tooltip>
            </TableCell>
            <TableCell colSpan={3}></TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};
