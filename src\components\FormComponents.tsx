import styled from '@emotion/styled';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';

// === General items ===

export const FormButton = (props: React.ComponentProps<typeof Button>) => (
  <Button color="primary" sx={{ ml: '0.5rem', mb: 0 }} {...props} />
);

export const MockButton = (props: React.ComponentProps<typeof Button>) => (
  <Button sx={{ mr: 1, mb: 0, bgcolor: '#8acd7b', color: '#706f6f' }} {...props} />
);

// === Table form items ===

export const TableFormInnerBox = styled(Box)`
  margin: 0.5rem 0;
  &.right {
    display: flex;
    justify-content: flex-end;
  }
`;
