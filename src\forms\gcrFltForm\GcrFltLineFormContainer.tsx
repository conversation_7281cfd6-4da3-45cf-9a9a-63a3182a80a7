import { useHandleAddLine } from '@src/utils/useHandleAddLine';
import { GcrFltLineFormTableRenderer } from './GcrFltLineFormTableRenderer';
import type { GcrFltLine, GcrFltMessage } from './gcrFltTypes';
import { gcrFltLineSchema } from './gcrFltValidation';
import { useGcrFltStore } from './useGcrFltStore';

// TODO: Much functionality has been moved out and almost makes this component obsolete.
/**
 * The 'GcrFltLineFormContainer' creates config and handlers for the line form
 * and passes them to the 'GcrFltLineFormTableRenderer'
 */
export const GcrFltLineFormContainer: React.FC = () => {
  const handleAddLine = useHandleAddLine<GcrFltLine, GcrFltMessage>({
    schema: gcrFltLineSchema,
    useStore: useGcrFltStore,
  });

  return <GcrFltLineFormTableRenderer handleAddLine={handleAddLine} />;
};
