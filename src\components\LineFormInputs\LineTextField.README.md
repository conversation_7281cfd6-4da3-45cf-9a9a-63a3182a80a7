# Generic LineTextField Component

This module provides a generic LineTextField component to eliminate code duplication across the three line form types (SCR, GCR FLT, GCR REG). The component creates consistent TextField behavior with minimal configuration using a generic component pattern instead of factories.

## Overview

Before the refactor, each form had ~50+ lines of nearly identical code for:

- Custom TextField components
- onChange handlers
- onKeyDown handlers for tab navigation and character selection
- Focus management
- Error handling

After the refactor, each form requires only ~9 lines of code using the generic LineTextField component with form-specific wrapper components.

## Generic Component Pattern

### `LineTextField<TLine, TMessage>`

A generic TextField component that works with any line form type.

**Props:**

```typescript
type LineTextFieldProps<TLine, TMessage> = React.ComponentProps<typeof TextField> & {
  currentInputKey: keyof TLine;
  useStore: () => BaseFormStore<TLine, TMessage>;
  formConfig: FormConfig<TLine>;
};
```

**Direct Usage:**

```typescript
import { LineTextField } from '@src/components/LineFormInputs/LineTextField';
import { scrLineFormConfig } from './scrLineFormConfig';
import type { ScrLine, ScrMessage } from './scrTypes';
import { useScrStore } from './useScrStore';

// Direct usage (verbose)
<LineTextField<ScrLine, ScrMessage> currentInputKey="action" useStore={useScrStore} formConfig={scrLineFormConfig} />;
```

### Form-Specific Wrapper Components

Each form has a wrapper component that automatically provides the store and config:

```typescript
import { LineTextField, type LineTextFieldProps } from '@src/components/LineFormInputs/LineTextField';
import { scrLineFormConfig } from './scrLineFormConfig';
import type { ScrLine, ScrMessage } from './scrTypes';
import { useScrStore } from './useScrStore';

type ScrLineTextFieldProps = Omit<LineTextFieldProps<ScrLine, ScrMessage>, 'useStore' | 'formConfig'>;

export const ScrLineTextField = (props: ScrLineTextFieldProps) => {
  return <LineTextField<ScrLine, ScrMessage> {...props} useStore={useScrStore} formConfig={scrLineFormConfig} />;
};
```

**Clean Usage:**

```typescript
// Simple usage with wrapper
<ScrLineTextField currentInputKey="action" />
<GcrFltTextField currentInputKey="operatorArr" />
<GcrRegLineTextField currentInputKey="aircraftRegistration" />
```

### Supporting Utilities

The LineTextField component uses `createLineFormUtils` internally for consistent behavior:

```typescript
import { createLineFormUtils } from '@src/components/LineFormInputs/createLineFormUtils';

// Used internally by LineTextField
const { createOnChange, createOnKeyHandler, inputs } = createLineFormUtils({ formConfig });
const onChange = createOnChange(currentInputKey, setFormLinePartial);
const onKeyDown = createOnKeyHandler(formConfig.inputs, currentInputKey, setFormLinePartial, setFormLineFocusKey);
```

## Features

### Automatic Field Type Detection

- **Select fields**: Automatically detected when `thisInput.type === 'select'`
- **Regular fields**: Text, number, etc.
- **Proper slotProps**: Different patterns for select vs regular fields for tabIndex

### Tab Navigation

- **Tab**: Move to next field in `tabOrder`
- **Shift+Tab**: Move to previous field in `tabOrder`
- **Focus management**: Automatic focus and visual indicators

### Character Selection (Select Fields)

- **Single key press**: Automatically selects matching option in select fields
- **Case insensitive**: Works with both uppercase and lowercase keys

### Value Transformation

- **Number fields**: Automatic string-to-number conversion
- **Uppercase fields**: Automatic uppercase transformation when `toUpperCase: true`

### Error Handling

- **Field-level errors**: Displays validation errors per field
- **Visual indicators**: Error styling and helper text

## Benefits

1. **Massive Code Reduction**: ~50+ lines → ~9 lines per form
2. **Consistency**: All forms behave identically
3. **Maintainability**: Changes only need to be made in one place
4. **Type Safety**: Full TypeScript support with proper generics
5. **Feature Parity**: All existing functionality preserved
6. **Easy Testing**: Generic component can be unit tested once
7. **No Factory Abstraction**: Direct component usage instead of factory pattern
8. **Better IDE Support**: Standard React component pattern with better autocomplete

## Current Implementation

All three forms now use the generic LineTextField component with form-specific wrappers:

- **SCR Form**: `src/forms/scrForm/SrcLineTextField.tsx`
- **GCR FLT Form**: `src/forms/gcrFltForm/GcrFltTextField.tsx`
- **GCR REG Form**: `src/forms/gcrRegForm/GcrRegLineTextField.tsx`

Each form's TextField component is now a simple wrapper that provides the store and config automatically.

## Architecture

```
Generic LineTextField Component
├── ScrLineTextField (wrapper)
├── GcrFltTextField (wrapper)
└── GcrRegLineTextField (wrapper)
```

The generic component handles all the complex logic while the wrappers provide a clean, form-specific API.

## Migration from Factory Pattern

The previous factory pattern (`createLineTextField`) has been replaced with this generic component approach for better developer experience and maintainability. The factory pattern created an abstraction layer that made the code harder to understand and debug.
