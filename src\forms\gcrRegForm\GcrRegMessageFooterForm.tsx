import Add from '@mui/icons-material/Add';
import Close from '@mui/icons-material/Close';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import { FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { DEBUG } from '@src/constants';
import { zodErrorMap } from '@src/utils/utils';
import { useRef } from 'react';
import { gcrRegMessageFormConfig as formConfig } from './gcrRegMessageFormConfig';
import { mockMessages } from './gcrRegMockData';
import { useGcrRegStore } from './gcrRegStore';
import { gcrRegMessageSchema } from './gcrRegValidation';

export const GcrRegMessageFooterForm: React.FC = () => {
  const { inputs } = formConfig;
  const {
    setMessage,
    formMessage,
    setFormMessagePartial: setMsgPart,
    clearFormMessage,
    formMessageErrors: errors,
    setFormMessageErrors,
    clearFormMessageErrors,
  } = useGcrRegStore();

  const formRef = useRef<HTMLDivElement>(null);

  const handleAddMessage = () => {
    const parsed = gcrRegMessageSchema.safeParse(formMessage);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      setFormMessageErrors(errorMap);
      return;
    }
    clearFormMessageErrors();
    setMessage(parsed.data);
  };

  return (
    <div ref={formRef}>
      <Table /* component={Paper} */ size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <TableHead>
          <TableRow>
            <TableCell colSpan={4} sx={{ textAlign: 'left' }}>
              Footer
            </TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Scheduled Information:
            </TableCell>
            <TableCell colSpan={3}>
              <Tooltip title={errors?.si}>
                <TextField
                  name="message.si"
                  multiline
                  minRows={2}
                  maxRows={2}
                  // value={formMessage?.si || ''}
                  // Not controlling input value for performance reasons:
                  onBlur={(e) => {
                    setMsgPart({ si: e.target.value });
                  }}
                  error={!!errors?.si}
                />
              </Tooltip>
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              General Information:
            </TableCell>
            <TableCell colSpan={3}>
              <Tooltip title={errors?.gi}>
                <TextField
                  name="message.gi"
                  multiline
                  minRows={2}
                  maxRows={2}
                  // value={formMessage?.gi || ''}
                  // Not controlling input value for performance reasons:
                  onBlur={(e) => {
                    setMsgPart({ gi: e.target.value });
                  }}
                  error={!!errors?.gi}
                />
              </Tooltip>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
      {DEBUG && (
        <TableFormInnerBox className="right">
          {mockMessages.map((msg, i) => (
            <MockButton key={i} onClick={() => setMsgPart(msg)}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}
      <TableFormInnerBox className="right">
        <FormButton
          color="secondary"
          startIcon={<Close />}
          onClick={() => {
            clearFormMessage();
            // Reset uncontrolled inputs (input value not controlled by useStore):
            Object.values(inputs).forEach((input) => {
              const name = `message.${input.storeKey}`;
              const inputElm = document.querySelector(`[name="${name}"]`) as HTMLInputElement;
              if (inputElm?.value) inputElm.value = '';
            });
            clearFormMessageErrors();
          }}
        >
          Clear
        </FormButton>
        <FormButton startIcon={<Add />} onClick={handleAddMessage}>
          Update Message
        </FormButton>
      </TableFormInnerBox>
    </div>
  );
};
