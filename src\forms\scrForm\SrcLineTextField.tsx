import { LineTextField, type LineTextFieldProps } from '@src/components/LineFormInputs/LineTextField';
import { scrLineFormConfig } from './scrLineFormConfig';
import { useScrStore } from './useScrStore';
import type { ScrLine, ScrMessage } from './scrTypes';

type ScrLineTextFieldProps = Omit<LineTextFieldProps<ScrLine, ScrMessage>, 'useStore' | 'formConfig'>;

export const ScrLineTextField = (props: ScrLineTextFieldProps) => {
  return <LineTextField<ScrLine, ScrMessage> {...props} useStore={useScrStore} formConfig={scrLineFormConfig} />;
};
