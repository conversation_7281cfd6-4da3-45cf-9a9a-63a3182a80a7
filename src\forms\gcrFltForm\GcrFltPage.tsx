import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { GcrFltLineForm } from './GcrFltLineForm';
import { GcrFltMessageFooterForm } from './GcrFltMessageFooterForm';
import { GcrFltMessageHeaderForm } from './GcrFltMessageHeaderForm';
import { GcrFltMessageOutput } from './GcrFltMessageOutput';
import { formatGcrFltLine } from './gcrFltFormatting';
import { useGcrFltStore } from './useGcrFltStore';

export const GcrFltPage: React.FC = () => {
  return (
    <GenericFormPage
      messageHeaderForm={<GcrFltMessageHeaderForm />}
      lineForm={<GcrFltLineForm />}
      lineList={<LineList useStore={useGcrFltStore} formatLine={formatGcrFltLine} />}
      messageFooterForm={<GcrFltMessageFooterForm />}
      messageOutput={<GcrFltMessageOutput />}
    />
  );
};
