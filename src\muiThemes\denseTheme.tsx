import { createTheme } from '@mui/material/styles';

// This theme is not used at the moment
export const denseTheme = createTheme({
  spacing: 2,
  typography: {
    fontSize: 12,
    h1: { fontSize: '2rem' }, // Default is 6rem
    h2: { fontSize: '1.5rem' }, // Default is 3.75rem
    h3: { fontSize: '1.2rem' }, // Default is 3rem
    h4: { fontSize: '1.1rem' }, // Default is 2.125rem
    h5: { fontSize: '1rem' }, // Default is 1.5rem
    h6: { fontSize: '0.9rem' }, // Default is 1.25rem
  },
  components: {
    MuiTextField: {
      defaultProps: {
        fullWidth: true,
        size: 'small',
        variant: 'outlined',
        style: { backgroundColor: 'white' },
      },
      styleOverrides: {
        root: {
          marginBottom: 2,
        },
      },
    },
    MuiInputBase: {
      styleOverrides: {
        input: {
          padding: '2px 4px',
          fontSize: '0.75rem',
          margin: '2px 4px',
        },
      },
    },
    MuiFormControl: {
      styleOverrides: {
        root: {
          margin: '2px 4px',
        },
      },
    },
    MuiButton: {
      defaultProps: {
        size: 'small',
      },
      styleOverrides: {
        root: {
          minHeight: 20,
          padding: '2px 8px',
          fontSize: '0.75rem',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          padding: '2px 6px',
          fontSize: '0.75rem',
        },
        head: {
          backgroundColor: '#1976d2',
          color: 'white',
          fontWeight: 600,
          textAlign: 'center',
        },
      },
    },
  },
});
