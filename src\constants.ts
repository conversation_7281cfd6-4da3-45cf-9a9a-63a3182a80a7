import { Home } from './components/HomePage';
import { GcrFltPage } from './forms/gcrFltForm/GcrFltPage';
import { GcrRegPage } from './forms/gcrRegForm/GcrRegPage';
import { ScrPage } from './forms/scrForm/ScrPage';

/**
 * Flag to enable debug mode.
 * Set in browser javascript console with this command:
 * localStorage.setItem('DEBUG', 'true')
 * Disable with this command:
 * localStorage.removeItem('DEBUG')
 */
export const DEBUG = !!localStorage.getItem('DEBUG');

/** List of all pages and their associated param values and components. */
export const appPages = [
  { name: 'Aviation Messages', pageParam: 'home', component: Home },
  { name: 'SCR', pageParam: 'scr', component: ScrPage },
  { name: 'GCR FLT', pageParam: 'gcrFlt', component: GcrFltPage },
  { name: 'G<PERSON> Reg', pageParam: 'gcrReg', component: GcrRegPage },
];

/** Query parameter used to store the current page in the URL. */
export const PAGE_QUERY_PARAM = 'diyssimPage';

/** Prefix used for errors that are not related to a specific input field: */
export const StructuralPrefix = 'STRUCTURAL_';
