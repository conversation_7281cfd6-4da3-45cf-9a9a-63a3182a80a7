import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { ScrLineForm } from './ScrLineForm';
import { ScrMessageFooterForm } from './ScrMessageFooterForm';
import { ScrMessageHeaderForm } from './ScrMessageHeaderForm';
import { ScrMessageOutputContainer } from './ScrMessageOutputContainer';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrPage: React.FC = () => {
  return (
    <GenericFormPage
      messageHeaderForm={<ScrMessageHeaderForm />}
      lineForm={<ScrLineForm />}
      lineList={<LineList useStore={useScrStore} formatLine={formatScrLine} />}
      messageFooterForm={<ScrMessageFooterForm />}
      messageOutput={<ScrMessageOutputContainer />}
    />
  );
};
