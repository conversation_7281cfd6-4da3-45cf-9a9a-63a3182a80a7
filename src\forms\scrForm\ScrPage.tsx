import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { ScrLineFormTableRenderer } from './ScrLineFormTableRenderer';
import { ScrMessageFormFooterRenderer } from './ScrMessageFormFooterRenderer';
import { ScrMessageFormHeaderRenderer } from './ScrMessageFormHeaderRenderer';
import { ScrMessageOutputContainer } from './ScrMessageOutputContainer';
import { formatScrLine } from './scrUtils';
import { useScrStore } from './useScrStore';

export const ScrPage: React.FC = () => {
  return (
    <GenericFormPage
      messageHeaderForm={<ScrMessageFormHeaderRenderer />}
      lineForm={<ScrLineFormTableRenderer />}
      lineList={<LineList useStore={useScrStore} formatLine={formatScrLine} />}
      messageFooterForm={<ScrMessageFormFooterRenderer />}
      messageOutput={<ScrMessageOutputContainer />}
    />
  );
};
