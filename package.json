{"name": "diyssim", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "commit-msg": "node scripts/commit-message.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "immer": "^10.1.1", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "zod": "^3.25.13", "zod-form-data": "^2.0.7", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/lodash": "^4.17.17", "@types/node": "^22.15.29", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-checker": "^0.9.3"}}