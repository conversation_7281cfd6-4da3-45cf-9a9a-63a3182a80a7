#!/usr/bin/env node

/**
 * Commit Message Generator CLI
 * 
 * Generates consistent commit messages for staged files
 * Usage: node scripts/commit-message.js [options]
 */

import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import the generator (we'll need to compile TypeScript first or use a different approach)
// For now, let's implement the core logic directly in JavaScript

/**
 * Parses git status porcelain output
 */
function parseGitStatus(statusOutput) {
  return statusOutput
    .split('\n')
    .filter(line => line.trim())
    .map(line => {
      const status = line.substring(0, 2).trim();
      const path = line.substring(3);
      return { status, path };
    });
}

/**
 * Gets filename from path
 */
function getFileName(path) {
  return path.split('/').pop() || path.split('\\').pop() || path;
}

/**
 * Determines file type description
 */
function getFileType(path) {
  const ext = path.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'ts':
    case 'tsx':
      return 'TypeScript file';
    case 'js':
    case 'jsx':
      return 'JavaScript file';
    case 'json':
      return 'configuration';
    case 'md':
      return 'documentation';
    case 'css':
    case 'scss':
      return 'styles';
    case 'html':
      return 'template';
    case 'yml':
    case 'yaml':
      return 'configuration';
    default:
      return '';
  }
}

/**
 * Gets description for a group of files
 */
function getFileTypeDescription(changes) {
  const paths = changes.map(c => c.path);
  
  // Check for common patterns
  if (paths.some(p => p.includes('Form') && p.includes('Config'))) {
    return 'form configuration';
  }
  
  if (paths.some(p => p.includes('component') || p.includes('Component'))) {
    return 'components';
  }
  
  if (paths.some(p => p.includes('util') || p.includes('Utils'))) {
    return 'utilities';
  }
  
  if (paths.some(p => p.includes('store') || p.includes('Store'))) {
    return 'store configuration';
  }
  
  if (paths.some(p => p.includes('validation') || p.includes('Validation'))) {
    return 'validation';
  }

  // Default to file count
  return `${changes.length} file${changes.length > 1 ? 's' : ''}`;
}

/**
 * Generates the header line based on file changes
 */
function generateHeader(changes, categorized) {
  const { added, modified, deleted, renamed } = categorized;
  const totalChanges = changes.length;

  // Determine primary action
  if (totalChanges === 1) {
    const change = changes[0];
    const fileName = getFileName(change.path);
    
    switch (change.status) {
      case 'A':
        return `Add ${fileName}`;
      case 'M':
        return `Update ${fileName}`;
      case 'D':
        return `Remove ${fileName}`;
      case 'R':
        return `Rename ${fileName}`;
      default:
        return `Modify ${fileName}`;
    }
  }

  // Multiple files - determine dominant action
  if (modified.length > added.length && modified.length > deleted.length) {
    return `Update ${getFileTypeDescription(modified)}`;
  }
  
  if (added.length > 0 && deleted.length === 0) {
    return `Add ${getFileTypeDescription(added)}`;
  }
  
  if (deleted.length > 0 && added.length === 0) {
    return `Remove ${getFileTypeDescription(deleted)}`;
  }
  
  if (added.length > 0 && deleted.length > 0) {
    return `Refactor ${getFileTypeDescription(changes)}`;
  }

  return `Update ${getFileTypeDescription(changes)}`;
}

/**
 * Generates bullets for a specific category of changes
 */
function generateCategoryBullets(action, changes, maxDetails = 5) {
  if (changes.length === 0) return [];

  const bullets = [];
  const displayChanges = changes.slice(0, maxDetails);
  
  displayChanges.forEach(change => {
    const fileName = getFileName(change.path);
    const fileType = getFileType(change.path);
    
    if (fileType) {
      bullets.push(`- ${action} ${fileName} ${fileType}`);
    } else {
      bullets.push(`- ${action} ${fileName}`);
    }
  });

  // Add summary if there are more files
  if (changes.length > maxDetails) {
    const remaining = changes.length - maxDetails;
    bullets.push(`- ${action} ${remaining} additional file${remaining > 1 ? 's' : ''}`);
  }

  return bullets;
}

/**
 * Generates bullet points for the commit message
 */
function generateBullets(changes, categorized, maxDetails = 5) {
  const { added, modified, deleted, renamed } = categorized;
  const bullets = [];

  // Add bullets for each category
  if (added.length > 0) {
    bullets.push(...generateCategoryBullets('Add', added, maxDetails));
  }
  
  if (modified.length > 0) {
    bullets.push(...generateCategoryBullets('Update', modified, maxDetails));
  }
  
  if (deleted.length > 0) {
    bullets.push(...generateCategoryBullets('Remove', deleted, maxDetails));
  }
  
  if (renamed.length > 0) {
    bullets.push(...generateCategoryBullets('Rename', renamed, maxDetails));
  }

  return bullets;
}

/**
 * Main commit message generator
 */
function generateCommitMessage(changes, options = {}) {
  const { customHeader, context, maxFileDetails = 5 } = options;

  if (changes.length === 0) {
    return 'Update project files';
  }

  // Categorize changes
  const added = changes.filter(c => c.status === 'A');
  const modified = changes.filter(c => c.status === 'M');
  const deleted = changes.filter(c => c.status === 'D');
  const renamed = changes.filter(c => c.status === 'R');

  // Generate header
  const header = customHeader || generateHeader(changes, { added, modified, deleted, renamed });

  // Generate bullet points
  const bullets = generateBullets(changes, { added, modified, deleted, renamed }, maxFileDetails);

  // Combine header and bullets
  const message = [header, ...bullets].join('\n');

  return context ? `${message}\n\n${context}` : message;
}

/**
 * Main CLI function
 */
function main() {
  try {
    // Get staged files
    const stagedOutput = execSync('git diff --cached --name-status', { encoding: 'utf8' });
    
    if (!stagedOutput.trim()) {
      console.log('No staged files found. Stage some files first with: git add <files>');
      process.exit(1);
    }

    // Parse staged files
    const stagedChanges = stagedOutput
      .split('\n')
      .filter(line => line.trim())
      .map(line => {
        const parts = line.split('\t');
        return { status: parts[0], path: parts[1] };
      });

    // Parse command line arguments
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i++) {
      if (args[i] === '--header' && args[i + 1]) {
        options.customHeader = args[i + 1];
        i++;
      } else if (args[i] === '--context' && args[i + 1]) {
        options.context = args[i + 1];
        i++;
      } else if (args[i] === '--max-files' && args[i + 1]) {
        options.maxFileDetails = parseInt(args[i + 1]);
        i++;
      }
    }

    // Generate commit message
    const commitMessage = generateCommitMessage(stagedChanges, options);
    
    console.log('Generated commit message:');
    console.log('========================');
    console.log(commitMessage);
    console.log('========================');
    console.log('\nTo use this message:');
    console.log(`git commit -m "${commitMessage.replace(/"/g, '\\"')}"`);
    
  } catch (error) {
    console.error('Error generating commit message:', error.message);
    process.exit(1);
  }
}

// Show help if requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Commit Message Generator

Usage: node scripts/commit-message.js [options]

Options:
  --header <text>     Custom header line
  --context <text>    Additional context to append
  --max-files <num>   Maximum files to detail (default: 5)
  --help, -h          Show this help

Examples:
  node scripts/commit-message.js
  node scripts/commit-message.js --header "Fix validation bug"
  node scripts/commit-message.js --context "Resolves issue #123"
  `);
  process.exit(0);
}

main();
