import Grid from '@mui/material/Grid';
import { PageSectionWrap } from './PageLayout';

type GenericFormPageProps = {
  messageHeaderForm: React.ReactNode;
  lineForm: React.ReactNode;
  lineList: React.ReactNode;
  messageFooterForm: React.ReactNode;
  messageOutput: React.ReactNode;
};

/**
 * Generic form page layout component.
 * Agnostic to the specific form types.
 */
export const GenericFormPage = (props: GenericFormPageProps) => {
  const { messageHeaderForm, lineForm, lineList, messageFooterForm, messageOutput } = props;

  return (
    <>
      <PageSectionWrap>{messageHeaderForm}</PageSectionWrap>
      <PageSectionWrap>{lineForm}</PageSectionWrap>
      <PageSectionWrap>{lineList}</PageSectionWrap>
      <PageSectionWrap>
        <Grid container spacing={1} sx={{ alignItems: 'flex-start', justifyContent: 'space-between' }}>
          <Grid size={5.8}>{messageFooterForm}</Grid>
          <Grid size={5.8}>{messageOutput}</Grid>
        </Grid>
      </PageSectionWrap>
    </>
  );
};
