import { z } from 'zod';
import type { LineAction } from './validationSchemas';

/**
 * Generic type for any line that has an action property
 */
export interface LineWithAction {
  action: LineAction;
}

// TODO: Consider simpler algorithm: Concat actions to string, and validate with regex.
/**
 * Validates the C/R action sequence rules for any array of lines with action property.
 * It either updates the zod context or returns an error map.
 *
 * Rules:
 * 1. A section of one or more C lines must always be followed directly by one or more R lines -
 *    in direct succession, ie no other line types between them.
 * 2. A section of one or more R lines must always be preceded by at least one or more C lines -
 *    in direct succession, ie no other line types between them.
 */
export const validateActionSequence = <TLine extends LineWithAction>(
  lines: TLine[],
  ctx?: z.RefinementCtx,
): void | Record<string, string> => {
  if (lines.length === 0) return;
  const errorMap: Record<string, string> = {};

  // How this validation works:
  // 1. First, we scan through all lines and group consecutive actions into "sections"
  //    Example: [C, C, R, N, C, R, R] becomes sections: [C-section, R-section, OTHER-section, C-section, R-section]
  // 2. Then we validate that every C-section is immediately followed by an R-section
  //    and every R-section is immediately preceded by a C-section
  // 3. This ensures no other line types (N, D) can appear between C and R actions

  // Group consecutive C and R sections
  const sections: { type: 'C' | 'R' | 'OTHER'; startIndex: number; endIndex: number }[] = [];
  let currentSection: { type: 'C' | 'R' | 'OTHER'; startIndex: number; endIndex: number } | null = null;

  // Iterate through lines to group consecutive actions into sections
  // (e.g., "C,C,C" becomes one C section, "R,R" becomes one R section)
  for (let i = 0; i < lines.length; i++) {
    const action = lines[i].action;
    const sectionType = action === 'C' ? 'C' : action === 'R' ? 'R' : 'OTHER';

    if (!currentSection || currentSection.type !== sectionType) {
      // Start new section
      if (currentSection) {
        sections.push(currentSection);
      }
      currentSection = { type: sectionType, startIndex: i, endIndex: i };
    } else {
      // Extend current section
      currentSection.endIndex = i;
    }
  }

  // Add the last section
  if (currentSection) {
    sections.push(currentSection);
  }

  // Validate the sections
  for (let i = 0; i < sections.length; i++) {
    const section = sections[i];

    // Rule 1: C section must be followed directly by R section
    if (section.type === 'C') {
      const nextSection = sections[i + 1];
      const message = 'A section of one or more C lines must be followed directly by one or more R lines.';
      if (!nextSection || nextSection.type !== 'R') {
        ctx
          ? ctx.addIssue({
              path: ['lines'],
              code: z.ZodIssueCode.custom,
              message,
            })
          : (errorMap['C lines'] = message);
      }
    }

    // Rule 2: R section must be preceded directly by C section
    if (section.type === 'R') {
      const prevSection = sections[i - 1];
      const message = 'A section of one or more R lines must be preceded directly by one or more C lines.';
      if (!prevSection || prevSection.type !== 'C') {
        ctx
          ? ctx.addIssue({
              path: ['lines'],
              code: z.ZodIssueCode.custom,
              message,
            })
          : (errorMap['R lines'] = message);
      }
    }
  }

  return errorMap;
};
