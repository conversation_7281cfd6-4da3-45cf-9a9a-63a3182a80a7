import { LineTextField, type LineTextFieldProps } from '@src/components/LineFormInputs/LineTextField';
import { gcrRegLineFormConfig } from './gcrRegLineFormConfig';
import type { GcrRegLine, GcrRegMessage } from './gcrRegTypes';
import { useGcrRegStore } from './gcrRegStore';

type GcrRegLineTextFieldProps = Omit<LineTextFieldProps<GcrRegLine, GcrRegMessage>, 'useStore' | 'formConfig'>;

export const GcrRegLineTextField = (props: GcrRegLineTextFieldProps) => {
  return (
    <LineTextField<GcrRegLine, GcrRegMessage> {...props} useStore={useGcrRegStore} formConfig={gcrRegLineFormConfig} />
  );
};
