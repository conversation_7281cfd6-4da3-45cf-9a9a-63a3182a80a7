import { GenericFormPage } from '@src/components/GenericFormPage';
import { LineList } from '@src/components/LineList';
import { GcrRegLineForm } from './GcrRegLineForm';
import { GcrRegMessageFooterForm } from './GcrRegMessageFooterForm';
import { GcrRegMessageHeaderForm } from './GcrRegMessageHeaderForm';
import { GcrRegMessageOutput } from './GcrRegMessageOutput';
import { useGcrRegStore } from './gcrRegStore';
import { formatGcrRegLine } from './gcrRegUtils';

export const GcrRegPage: React.FC = () => {
  return (
    <GenericFormPage
      messageHeaderForm={<GcrRegMessageHeaderForm />}
      lineForm={<GcrRegLineForm />}
      lineList={<LineList useStore={useGcrRegStore} formatLine={formatGcrRegLine} />}
      messageFooterForm={<GcrRegMessageFooterForm />}
      messageOutput={<GcrRegMessageOutput />}
    />
  );
};
