TODOS

x Remove all the variant="contained" and size="small" from buttons and textfields

- <PERSON><PERSON> comps with "Renderer"
- Refactor and delete obsolete form containers
- Refactor and delete obsolete message preview containers etc
- Rename scrUtils etc to more suitable name such as formatScrMessage etc
- Refactor createLineTextField factory to generic LineTextField comp instead
- Update readme file
x Delete DanOles-README.md
- Consider what to do with vite-README.md
- Handle duplicate StructuralPrefix const.


Why does AI suggest this?
- Remove all the sx={{ ml: '0.5rem', mb: 0 }} from buttons
- Remove all the sx={{ mb: 2 }} from textfields
- Remove all the sx={{ mt: 2 }} from form headers
- Remove all the sx={{ mb: 2 }} from form footers
- Remove all the sx={{ mb: 2 }} from form sections