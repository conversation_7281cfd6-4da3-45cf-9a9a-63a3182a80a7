TODOS

- Update readme file
- Remove all use of !important in theme if possible, it was AI generated
- Consider refactor message outputs to use generic hook and no wrapper component
- Consider improvement of validateActionSequence: Concat actions to string, and validate with regex.
- Consider to move line sequence validation away from message output, only show in list.
    However, it makes sense now, because the will know why no output is generated.
- Consider what to do with vite-README.md
x Refactor createLineTextField factory to generic LineTextField comp instead
x Rename scrUtils etc to more suitable name such as formatScrMessage etc
x Handle duplicate StructuralPrefix const.
x Rename comps with "Renderer"
x Refactor and delete obsolete form containers
x Refactor and delete obsolete message preview containers etc
x Rename message output containers
x Delete DanOles-README.md
x Remove all the variant="contained" and size="small" from buttons and textfields


Why does AI suggest this?
- Remove all the sx={{ ml: '0.5rem', mb: 0 }} from buttons
- Remove all the sx={{ mb: 2 }} from textfields
- Remove all the sx={{ mt: 2 }} from form headers
- Remove all the sx={{ mb: 2 }} from form footers
- Remove all the sx={{ mb: 2 }} from form sections