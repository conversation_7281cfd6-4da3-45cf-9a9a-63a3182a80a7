TODOS

- Refactor message output containers
- Rename scrUtils etc to more suitable name such as formatScrMessage etc
- Refactor createLineTextField factory to generic LineTextField comp instead
- Update readme file
- Handle duplicate StructuralPrefix const.
- Consider what to do with vite-README.md
x Rename comps with "Renderer"
x Refactor and delete obsolete form containers
x Refactor and delete obsolete message preview containers etc
x Rename message output containers
x Delete DanOles-README.md
x Remove all the variant="contained" and size="small" from buttons and textfields


Why does AI suggest this?
- Remove all the sx={{ ml: '0.5rem', mb: 0 }} from buttons
- Remove all the sx={{ mb: 2 }} from textfields
- Remove all the sx={{ mt: 2 }} from form headers
- Remove all the sx={{ mb: 2 }} from form footers
- Remove all the sx={{ mb: 2 }} from form sections