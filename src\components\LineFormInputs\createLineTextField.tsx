import { MenuItem, TextField } from '@mui/material';
import Tooltip from '@mui/material/Tooltip';
import { type BaseFormStore } from '@src/formStore/formStoreTypes';
import type { FormConfig, FormConfigInput } from '@src/typesGlobal';
import React from 'react';
import { createLineFormUtils } from './createLineFormUtils';

/**
 * Configuration for creating a line TextField component
 */
type LineTextFieldConfig<TLine, TMessage> = {
  formConfig: FormConfig<TLine>;
  useStore: () => BaseFormStore<TLine, TMessage>;
};

/**
 * Factory function to create a custom MUI TextField form input component
 * for line forms. Agnostic to the specific line and message types.
 * This component handles change events, key events, error display,
 * and focus management.
 * The Zustand store keeps track of the current focused input field,
 * and this component sets the focused state accordingly.
 */
export const createLineTextField = <TLine, TMessage>(config: LineTextFieldConfig<TLine, TMessage>) => {
  const { formConfig, useStore } = config;
  const utils = createLineFormUtils({ formConfig });

  type LineTextFieldProps = React.ComponentProps<typeof TextField> & {
    currentInputKey: keyof TLine;
  };

  const LineTextField: React.FC<LineTextFieldProps> = (props) => {
    const { currentInputKey, ...rest } = props;
    const thisInput: FormConfigInput<TLine> = utils.inputs[currentInputKey];

    const {
      formLine,
      formLineErrors: errors,
      setFormLinePartial: updateLine,
      formLineFocusKey,
      setFormLineFocusKey,
    } = useStore();

    // Check if this is a select field:
    const isSelect = thisInput.type === 'select';
    // Check for errors:
    const error = errors?.[currentInputKey as string] || '';
    const hasError = !!error;

    return (
      <Tooltip title={error}>
        <TextField
          label={thisInput.label}
          name={thisInput.storeKey as string}
          select={isSelect}
          value={formLine?.[currentInputKey] || ''}
          onChange={utils.createOnChange(currentInputKey, updateLine)}
          onKeyDown={utils.createOnKeyHandler(utils.inputs, currentInputKey, updateLine, setFormLineFocusKey)}
          focused={formLineFocusKey === currentInputKey}
          error={hasError}
          {...rest}
        >
          {isSelect &&
            thisInput.options?.map((option) => (
              <MenuItem key={option} value={option}>
                {option === '' ? '\u00A0' : option} {/* Non-breaking space for full height of option */}
              </MenuItem>
            ))}
        </TextField>
      </Tooltip>
    );
  };

  return LineTextField;
};
