import type { BaseFormStore } from '@src/formStore/formStoreTypes';
import { zodErrorMap } from '@src/utils/utils';
import { stripEmptyStringsFromInput } from '@src/validation/utils';
import { useCallback } from 'react';
import type { z, ZodTypeDef } from 'zod';

/**
 * Configuration for the useHandleAddLine hook
 */
type UseHandleAddLineConfig<TLine, TMessage> = {
  /** The Zod schema to validate the line - accepts any schema that outputs TLine */
  schema: z.ZodType<TLine, ZodTypeDef, unknown>;
  /** The store hook that returns the form store */
  useStore: () => BaseFormStore<TLine, TMessage>;
};

/**
 * Custom hook that creates a memoized handleAddLine function for line forms.
 * This hook extracts the common logic for validating and adding/updating lines
 * across different form types (SCR, GCR FLT, GCR REG).
 * @returns Memoized handleAddLine function
 */
export const useHandleAddLine = <TLine, TMessage>(config: UseHandleAddLineConfig<TLine, TMessage>) => {
  const { schema, useStore } = config;

  const { formLine, formLineIndex, addLine, updateLine, clearFormLine, setFormLineErrors, clearFormLineErrors } =
    useStore();

  const isEdit = formLineIndex !== null;

  /**
   * 'handleAddLine' will validate the formLine and add it to the store.
   * Or in case of invalid input, it will set the formLineErrors.
   * Memoized to prevent unnecessary re-renders.
   */
  const handleAddLine = useCallback(() => {
    const cleanedFormLine = stripEmptyStringsFromInput(formLine);
    const parsed = schema.safeParse(cleanedFormLine);
    if (!parsed.success) {
      const errorMap = zodErrorMap(parsed.error);
      setFormLineErrors(errorMap);
      return;
    }
    clearFormLineErrors();
    isEdit ? updateLine(parsed.data as Partial<TLine>) : addLine(parsed.data);
    clearFormLine();
  }, [addLine, clearFormLine, clearFormLineErrors, formLine, isEdit, schema, setFormLineErrors, updateLine]);

  return handleAddLine;
};
