import { useHandleAddLine } from '@src/utils/useHandleAddLine';
import { ScrLineFormTableRenderer } from './ScrLineFormTableRenderer';
import type { ScrLine, ScrMessage } from './scrTypes';
import { scrLineSchema } from './scrValidation';
import { useScrStore } from './useScrStore';

// TODO: Much functionality has been moved out and almost makes this component obsolete.
/**
 * The 'ScrLineFormContainer' creates an add line handler and passes
 * it to the 'ScrLineFormTableRenderer' component.
 */
export const ScrLineFormContainer: React.FC = () => {
  const handleAddLine = useHandleAddLine<ScrLine, ScrMessage>({
    schema: scrLineSchema,
    useStore: useScrStore,
  });

  return <ScrLineFormTableRenderer handleAddLine={handleAddLine} />;
};
