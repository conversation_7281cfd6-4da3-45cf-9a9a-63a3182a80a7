import { Add, Close } from '@mui/icons-material';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { FormButton, MockButton, TableFormInnerBox } from '@src/components/FormComponents';
import { LineErrors } from '@src/components/LineErrors';
import { DEBUG } from '@src/constants';
import { mockLines } from './gcrFltMockData';
import { GcrFltTextField } from './GcrFltTextField';
import { useGcrFltStore } from './useGcrFltStore';

type ScrLineFormProps = {
  handleAddLine: () => void;
};

export const GcrFltLineFormTableRenderer: React.FC<ScrLineFormProps> = (props) => {
  const { handleAddLine } = props;

  const { formLineIndex, clearFormLine, setFormLinePartial } = useGcrFltStore();
  // console.log(`errors`, errors);

  const isEdit = formLineIndex !== null;

  return (
    <>
      <Table size="small" sx={{ tableLayout: 'fixed', width: '100%' }}>
        <colgroup>
          <col style={{ width: '2%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 5 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          {/* 10 */}
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '4%' }} />
          <col style={{ width: '3%' }} />
          <col style={{ width: '4%' }} />
          {/* 15 */}
        </colgroup>
        <TableHead>
          <TableRow>
            <TableCell></TableCell>
            <TableCell align="center">C/R/N/D</TableCell>
            <TableCell>Operator</TableCell>
            <TableCell>FltNo</TableCell>
            <TableCell>Date</TableCell>
            {/* 5 */}
            <TableCell align="center">Seats</TableCell>
            <TableCell align="center">A/C</TableCell>
            <TableCell align="center">Origin</TableCell>
            <TableCell align="center">Prev</TableCell>
            <TableCell align="center">Time</TableCell>
            {/* 10 */}
            <TableCell align="center">O/N</TableCell>
            <TableCell align="center">Next</TableCell>
            <TableCell align="center">Dest</TableCell>
            <TableCell align="center">St</TableCell>
            <TableCell align="center">Acreg</TableCell>
            {/* 15 */}
          </TableRow>
        </TableHead>
        <TableBody>
          <TableRow>
            <TableCell component="th" scope="row">
              Arr
            </TableCell>
            {/* Action */}
            <TableCell rowSpan={2}>
              <GcrFltTextField currentInputKey="action" />
            </TableCell>
            {/* Operator */}
            <TableCell>
              <GcrFltTextField currentInputKey="operatorArr" />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <GcrFltTextField currentInputKey="flightNumberArr" />
            </TableCell>
            {/* Date Range */}
            <TableCell rowSpan={2}>
              <GcrFltTextField currentInputKey="date" />
            </TableCell>
            {/* Seats */}
            <TableCell rowSpan={2}>
              <GcrFltTextField currentInputKey="seats" />
            </TableCell>
            {/* Aircraft Type */}
            <TableCell rowSpan={2}>
              <GcrFltTextField currentInputKey="aircraftType" />
            </TableCell>
            {/* Origin */}
            <TableCell>
              <GcrFltTextField currentInputKey="origin" />
            </TableCell>
            {/* Previous */}
            <TableCell>
              <GcrFltTextField currentInputKey="previous" />
            </TableCell>
            {/* Time 1 */}
            <TableCell>
              <GcrFltTextField currentInputKey="timeArr" />
            </TableCell>
            {/* O/N */}
            <TableCell>{/* O/N */}</TableCell>
            {/* Next */}
            <TableCell>{/* Next */}</TableCell>
            {/* Destination */}
            <TableCell>{/* Dest */}</TableCell>
            {/* Service Type*/}
            <TableCell>
              <GcrFltTextField currentInputKey="stArr" />
            </TableCell>
            {/* Aircraft Registration */}
            <TableCell rowSpan={2}>
              <GcrFltTextField currentInputKey="aircraftRegistration" />
            </TableCell>
          </TableRow>
          <TableRow>
            <TableCell component="th" scope="row">
              Dep
            </TableCell>
            {/* Action */}
            {/* rowSpan={2} */}
            {/* Operator */}
            <TableCell>
              <GcrFltTextField currentInputKey="operatorDep" />
            </TableCell>
            {/* Flight Number */}
            <TableCell>
              <GcrFltTextField currentInputKey="flightNumberDep" />
            </TableCell>
            {/* Date Range */}
            {/* rowSpan={2} */}
            {/* DOOP */}
            {/* rowSpan={2} */}
            {/* Seats */}
            {/* rowSpan={2} */}
            {/* Aircraft Type */}
            {/* rowSpan={2} */}
            {/* Origin */}
            <TableCell>{/* Origin */}</TableCell>
            {/* Previous */}
            <TableCell>{/* Prev */}</TableCell>
            {/* Time 2 */}
            <TableCell>
              <GcrFltTextField currentInputKey="timeDep" />
            </TableCell>
            {/* O/N */}
            <TableCell>
              {/* O/N */}
              <GcrFltTextField currentInputKey="on" />
            </TableCell>
            {/* Next */}
            <TableCell>
              {/* Next */}
              <GcrFltTextField currentInputKey="next" />
            </TableCell>
            {/* Destination */}
            <TableCell>
              {/* Dest */}
              <GcrFltTextField currentInputKey="destination" />
            </TableCell>
            {/* Service Type*/}
            <TableCell>
              <GcrFltTextField currentInputKey="stDep" />
            </TableCell>
            {/* Frequency */}
            {/* rowSpan={2} */}
            {/* Aircraft Registration */}
            {/* rowSpan={2} */}
          </TableRow>
        </TableBody>
      </Table>

      <LineErrors useStore={useGcrFltStore} />

      {DEBUG && (
        <TableFormInnerBox className="right">
          {mockLines.map((line, i) => (
            <MockButton key={i} onClick={() => setFormLinePartial({ ...line })}>
              {i}
            </MockButton>
          ))}
        </TableFormInnerBox>
      )}

      <TableFormInnerBox className="right">
        <FormButton color="secondary" onClick={() => clearFormLine()} startIcon={<Close />}>
          Clear
        </FormButton>
        <FormButton onClick={handleAddLine} startIcon={<Add />}>
          {isEdit ? `Update Line` : `Add Line`}
        </FormButton>
      </TableFormInnerBox>
    </>
  );
};
