import { LineTextField, type LineTextFieldProps } from '@src/components/LineFormInputs/LineTextField';
import { gcrFltLineFormConfig } from './gcrFltLineFormConfig';
import type { GcrFltLine, GcrFltMessage } from './gcrFltTypes';
import { useGcrFltStore } from './useGcrFltStore';

type GcrFltTextFieldProps = Omit<LineTextFieldProps<GcrFltLine, GcrFltMessage>, 'useStore' | 'formConfig'>;

export const GcrFltTextField = (props: GcrFltTextFieldProps) => {
  return (
    <LineTextField<GcrFltLine, GcrFltMessage> {...props} useStore={useGcrFltStore} formConfig={gcrFltLineFormConfig} />
  );
};
