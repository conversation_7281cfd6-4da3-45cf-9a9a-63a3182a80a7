import type { FormConfig } from '@src/typesGlobal';
import type { GcrRegLine } from './gcrRegTypes';
import { lineActionValues, serviceTypeValues } from '@src/validation/validationSchemas';

export const gcrRegLineFormConfig: FormConfig<GcrRegLine> = {
  inputs: {
    action: {
      storeKey: 'action',
      label: '',
      type: 'select',
      options: lineActionValues,
      tabOrder: 1,
    },
    aircraftRegistration: {
      storeKey: 'aircraftRegistration',
      type: 'text',
      label: '',
      toUpperCase: true,
      tabOrder: 2,
    },
    date: {
      storeKey: 'date',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 3,
    },
    seats: {
      storeKey: 'seats',
      label: '',
      type: 'number',
      tabOrder: 4,
    },
    aircraftType: {
      storeKey: 'aircraftType',
      label: '',
      type: 'text',
      tabOrder: 5,
    },
    origin: {
      storeKey: 'origin',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 6,
    },
    previous: {
      storeKey: 'previous',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 7,
    },
    timeArr: {
      storeKey: 'timeArr',
      label: '',
      type: 'text',
      tabOrder: 8,
    },
    timeDep: {
      storeKey: 'timeDep',
      label: '',
      type: 'text',
      tabOrder: 9,
    },
    on: {
      storeKey: 'on',
      label: '',
      type: 'number',
      tabOrder: 10,
    },
    next: {
      storeKey: 'next',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 11,
    },
    destination: {
      storeKey: 'destination',
      label: '',
      type: 'text',
      toUpperCase: true,
      tabOrder: 12,
    },
    stArr: {
      storeKey: 'stArr',
      label: '',
      type: 'select',
      options: serviceTypeValues,
      tabOrder: 13,
    },
    stDep: {
      storeKey: 'stDep',
      label: '',
      type: 'select',
      options: serviceTypeValues,
      tabOrder: 14,
    },
  },
};
