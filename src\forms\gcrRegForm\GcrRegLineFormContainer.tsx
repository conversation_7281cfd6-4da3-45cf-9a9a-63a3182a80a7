import { useHandleAddLine } from '@src/utils/useHandleAddLine';
import { GcrRegLineFormTableRenderer } from './GcrRegLineFormTableRenderer';
import type { GcrRegLine, GcrRegMessage } from './gcrRegTypes';
import { useGcrRegStore } from './gcrRegStore';
import { gcrRegLineSchema } from './gcrRegValidation';

// TODO: Much functionality has been moved out and almost makes this component obsolete.
/**
 * The 'GcrFltLineFormContainer' creates config and handlers for the line form
 * and passes them to the 'GcrFltLineFormTableRenderer'
 */
export const GcrRegLineFormContainer: React.FC = () => {
  const handleAddLine = useHandleAddLine<GcrRegLine, GcrRegMessage>({
    schema: gcrRegLineSchema,
    useStore: useGcrRegStore,
  });

  return <GcrRegLineFormTableRenderer handleAddLine={handleAddLine} />;
};
